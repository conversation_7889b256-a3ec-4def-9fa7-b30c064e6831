USE membercentral;
GO

-- Add getCreditAmountsForAuthorityAndType method to SWBROWSE component for MCDEV-9998
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @methodID int, @ajaxViewRTFID int;

	SELECT @ajaxViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Ajax'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Ajax')));

	EXEC dbo.ajax_addComponentMethodRights
		@componentName='SWBROWSE',
		@requestCFC='model.semWebCatalog.browse',
		@methodName='getCreditAmountsForAuthorityAndType',
		@resourceTypeFunctionIDList=@ajaxViewRTFID,
		@componentID=@componentID OUTPUT,
		@methodID=@methodID OUTPUT;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO
