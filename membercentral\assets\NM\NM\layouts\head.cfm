<cfoutput>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
	<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
	
	<link rel="stylesheet" href="/css/main.css" />
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') NEQ "direct">
		<link rel="stylesheet" href="/css/styles.css" />
		<link rel="stylesheet" href="/css/dropDown.css" />
		<link rel="stylesheet" href="/css/slideshow.css" />
		<script type="text/javascript" src="/javascript/jquery.slideshow.js"></script>
		
		<!--- Custom Font for template --->
		<link href='https://fonts.googleapis.com/css?family=Raleway:300' rel='stylesheet' type='text/css'>
		
		<!--- Highlight nav section --->
		<script type="text/javascript">
			function highlightSection(){
				var navHome					= $('##homeNav');
				var navAbout				= $('##aboutNav');
				var navFindAttorney	=	$('##findAnAttorneyNav');
				var navMembership		= $('##membershipNav');
				var navEducation		= $('##educationNav');
				var navPublications	= $('##publicationsNav');
				var navResources		= $('##resourcesNav');
				var navSponsors			= $('##sponsorsNav');
				var currentSection	=	'#event.getValue('mc_pageDefinition.sectionCode','')#';
				var currentPage			= '#event.getValue('mc_pageDefinition.pageName')#';
				
				if ( currentPage == 'Main' ){
					navHome.addClass('activeSection');
				}
				else{
					switch (currentSection){
						case "About":
							navAbout.addClass('activeSection');
						break;
						case "Education":
							navEducation.addClass('activeSection');
						break;
						case "FindanAttorney":
							navFindAttorney.addClass('activeSection');
						break;
						case "Membership":
							navMembership.addClass('activeSection');
						break;
						case "Publications":
							navPublications.addClass('activeSection');
						break;
						case "Resources":
							navResources.addClass('activeSection');
						break;
						case "Sponsors":
							navSponsors.addClass('activeSection');
						break;
					}
				}
			}
		</script>
		
		
		
	</cfif>

	<style type="text/css">
	  <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		  ##noRightsLoggedOut{display:none;}
			##noRightsLoggedIn{display:block;}
  	<cfelse>
			##noRightsLoggedOut{display:block;}
			##noRightsLoggedIn{display:none;}
	  </cfif>
	</style>
</cfoutput>