ALTER PROC dbo.tr_importInvoiceProfiles
@siteID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSolicitationMessages') IS NOT NULL 
		DROP TABLE #tmpSolicitationMessages;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfileUpdates') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfileUpdates;
	
	CREATE TABLE #tmpInvoiceProfiles (syncInvoiceProfileID int, profileName varchar(50), enableAutoPay bit, enforcePayOldest bit, 
		notifyEmail varchar(100), allowPartialPayment bit, numDaysDelinquent int, enableProcessingFeeDonation bit, 
		processFeeDonationDefaultSelect bit, syncSolicitationMessageID int, finalAction char(1));
	CREATE TABLE #tmpInvoiceProfilesMerchantProfiles (syncInvoiceProfileID int, useMerchantProfileID int, useInvoiceProfileID int);
	CREATE TABLE #tmpSolicitationMessages (syncSolicitationMessageID int, useMessageID int, title varchar(100), message varchar(800), finalAction char(1));
	CREATE TABLE #tmpDeleteInvoiceProfiles (invoiceProfileID int);
	CREATE TABLE #tmpDeleteInvoiceProfilesMerchantProfiles (linkID int);
	CREATE TABLE #tmpInvoiceProfileUpdates (invoiceProfileID int, currentNumDaysDelinquent int, newNumDaysDelinquent int);

	DECLARE @orgID int, @ins_delinquent int, @ins_closed int;
	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	select @ins_delinquent = statusID from dbo.tr_invoiceStatuses where [status] = 'Delinquent';
	select @ins_closed = statusID from dbo.tr_invoiceStatuses where [status] = 'Closed';

	INSERT INTO #tmpInvoiceProfiles (syncInvoiceProfileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, 
		numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, syncSolicitationMessageID, finalAction)
	select distinct profileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, numDaysDelinquent, 
		enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, finalAction
	from datatransfer.dbo.sync_tr_invoiceProfiles
	where orgID = @orgID
	and finalAction in ('A','C');

	INSERT INTO #tmpInvoiceProfilesMerchantProfiles (syncInvoiceProfileID, useMerchantProfileID, useInvoiceProfileID)
	select distinct sipmp.invoiceProfileID, sipmp.useMerchantProfileID, sipmp.useInvoiceProfileID
	from datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join datatransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileID = sipmp.invoiceProfileID
	where sipmp.orgID = @orgID
	and sip.finalAction in ('A','C');

	INSERT INTO #tmpSolicitationMessages (syncSolicitationMessageID, useMessageID, title, message, finalAction)
	SELECT messageID, useMessageID, title, message, finalAction
	FROM datatransfer.dbo.sync_tr_solicitationMessages
	WHERE siteID = @siteID;

	-- remove invoice profiles
	INSERT INTO #tmpDeleteInvoiceProfiles (invoiceProfileID)
	select ip.profileID
	from dbo.tr_invoiceProfiles as ip
	left outer join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = ip.profileName
	where ip.orgID = @orgID
	and sip.profileID is null;

	-- invoice profiles in use
	IF EXISTS (select 1 from #tmpDeleteInvoiceProfiles) BEGIN
		IF EXISTS (select 1 from dbo.tr_GLAccounts as g inner join #tmpDeleteInvoiceProfiles as tmp on tmp.invoiceProfileID = g.invoiceProfileID)
			RAISERROR('Invoice Profile is linked to GL Accounts.',16,1);
		
		IF EXISTS (select 1 from dbo.tr_invoices as i inner join #tmpDeleteInvoiceProfiles as tmp on tmp.invoiceProfileID = i.invoiceProfileID)
			RAISERROR('Invoice Profile is linked to Invoices.',16,1);
		
		IF EXISTS (
			select c.conditionID 
			from dbo.ams_virtualGroupConditions as c 
			inner join dbo.ams_virtualGroupConditionValues as cv on c.orgID = @orgID and cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
			inner join #tmpDeleteInvoiceProfiles as tmp on cast(tmp.invoiceProfileID as varchar(20)) = cv.conditionValue)
			RAISERROR('Invoice Profile is linked to Group Assignment Conditions.',16,1);
	END

	-- delete invoice profiles merchant profiles
	INSERT INTO #tmpDeleteInvoiceProfilesMerchantProfiles (linkID)
	select distinct ipmp.linkID
	from #tmpDeleteInvoiceProfiles as tmp
	inner join dbo.tr_invoiceProfilesMerchantProfiles as ipmp on ipmp.invoiceProfileID = tmp.invoiceProfileID
		union
	select distinct ipmp.linkID
	from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
	inner join #tmpInvoiceProfilesMerchantProfiles as tmp on tmp.useInvoiceProfileID = ipmp.invoiceProfileID;

	-- delinquent days changes
	INSERT INTO #tmpInvoiceProfileUpdates (invoiceProfileID, currentNumDaysDelinquent, newNumDaysDelinquent)
	select ip.profileID, isnull(ip.numDaysDelinquent,0), isnull(tmp.numDaysDelinquent,0)
	from #tmpInvoiceProfiles as tmp
	inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileName = tmp.profileName
	where isnull(ip.numDaysDelinquent,0) <> isnull(tmp.numDaysDelinquent,0);

	BEGIN TRAN;
		-- delete invoice profiles merchant profiles
		IF EXISTS (select 1 from #tmpDeleteInvoiceProfilesMerchantProfiles)
			DELETE ipmp
			FROM dbo.tr_invoiceProfilesMerchantProfiles as ipmp
			INNER JOIN #tmpDeleteInvoiceProfilesMerchantProfiles as tmp on tmp.linkID = ipmp.linkID;

		-- delete invoice profiles
		IF EXISTS (select 1 from #tmpDeleteInvoiceProfiles)
			DELETE ip
			FROM dbo.tr_invoiceProfiles as ip
			INNER JOIN #tmpDeleteInvoiceProfiles as tmp on tmp.invoiceProfileID = ip.profileID;

		-- new solicitation messages
		IF EXISTS (select 1 from #tmpSolicitationMessages where finalAction = 'A') BEGIN
			INSERT INTO dbo.tr_solicitationMessages (siteID, title, message)
			SELECT @siteID, title, message
			FROM #tmpSolicitationMessages
			WHERE finalAction = 'A'
				EXCEPT
			SELECT siteID, title, message
			FROM dbo.tr_solicitationMessages
			WHERE siteID = @siteID;

			UPDATE tmp
			SET tmp.useMessageID = pfm.messageID
			FROM #tmpSolicitationMessages AS tmp
			INNER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
				AND pfm.message = tmp.message
			WHERE tmp.finalAction = 'A';
		END

		-- new invoice profiles
		IF EXISTS (select 1 from #tmpInvoiceProfiles where finalAction = 'A') BEGIN
			INSERT INTO dbo.tr_invoiceProfiles (orgID, profileName, status, imageExt, enableAutoPay, enforcePayOldest, allowPartialPayment, notifyEmail, numDaysDelinquent,
				enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID)
			select @orgID, tmp.profileName, 'A', null, tmp.enableAutoPay, tmp.enforcePayOldest, tmp.allowPartialPayment, tmp.notifyEmail, nullif(tmp.numDaysDelinquent,0),
				tmp.enableProcessingFeeDonation, tmp.processFeeDonationDefaultSelect, pfm.useMessageID
			from #tmpInvoiceProfiles as tmp
			left outer join #tmpSolicitationMessages as pfm on pfm.syncSolicitationMessageID = tmp.syncSolicitationMessageID
			where tmp.finalAction = 'A'
			and not exists (select 1 from dbo.tr_invoiceProfiles where orgID = @orgID and profileName = tmp.profileName);
		END

		-- update invoice profiles
		IF EXISTS (select 1 from #tmpInvoiceProfiles where finalAction = 'C') BEGIN
			UPDATE ip
			SET ip.enableAutoPay = tmp.enableAutoPay,
				ip.enforcePayOldest = tmp.enforcePayOldest,
				ip.allowPartialPayment = tmp.allowPartialPayment,
				ip.notifyEmail = tmp.notifyEmail,
				ip.numDaysDelinquent = nullif(tmp.numDaysDelinquent,0),
				ip.enableProcessingFeeDonation = tmp.enableProcessingFeeDonation,
				ip.processFeeDonationDefaultSelect = tmp.processFeeDonationDefaultSelect,
				ip.solicitationMessageID = pfm.useMessageID
			FROM #tmpInvoiceProfiles as tmp
			INNER JOIN dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileName = tmp.profileName
			LEFT OUTER JOIN #tmpSolicitationMessages as pfm on pfm.syncSolicitationMessageID = tmp.syncSolicitationMessageID
			WHERE tmp.finalAction = 'C';
		END

		-- new invoice profiles merchant profiles
		IF EXISTS (select 1 from #tmpInvoiceProfilesMerchantProfiles) BEGIN
			INSERT INTO dbo.tr_invoiceProfilesMerchantProfiles (invoiceProfileID, merchantProfileID)
			select ip.profileID, tmpIPMP.useMerchantProfileID
			from #tmpInvoiceProfilesMerchantProfiles as tmpIPMP
			inner join #tmpInvoiceProfiles as tmpIP on tmpIP.syncInvoiceProfileID = tmpIPMP.syncInvoiceProfileID
			inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileName = tmpIP.profileName
			where not exists (select 1 from dbo.tr_invoiceProfilesMerchantProfiles where invoiceProfileID = ip.profileID and merchantProfileID = tmpIPMP.useMerchantProfileID);
		END
	
		IF EXISTS (select 1 from #tmpInvoiceProfileUpdates) BEGIN
			-- if numDaysDelinquent is blank, ensure no invoices are delinquent - move them to closed
			INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select i.invoiceID, getdate(), @ins_closed, @ins_delinquent, @recordedByMemberID
			from dbo.tr_invoices as i
			inner join #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
			where i.orgID = @orgID
			and i.statusID = @ins_delinquent
			and tmp.currentNumDaysDelinquent > 0
			and tmp.newNumDaysDelinquent = 0;

			IF @@ROWCOUNT > 0 BEGIN
				UPDATE i
				SET i.statusID = @ins_closed
				FROM dbo.tr_invoices as i
				INNER JOIN #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
				WHERE i.orgID = @orgID
				AND i.statusID = @ins_delinquent
				AND tmp.currentNumDaysDelinquent > 0
				AND tmp.newNumDaysDelinquent = 0;
			END

			-- else look at the invoices to see if we need to move to/from delinq
			INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select i.invoiceID, getdate(), @ins_delinquent, @ins_closed, @recordedByMemberID
			from dbo.tr_invoices as i
			inner join #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
			where i.orgID = @orgID
			and i.statusID = @ins_closed
			and tmp.newNumDaysDelinquent > 0
			and tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
			and i.dateDue <= DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());

			IF @@ROWCOUNT > 0 BEGIN
				UPDATE i
				SET i.statusID = @ins_delinquent
				FROM dbo.tr_invoices as i
				INNER JOIN #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
				WHERE i.orgID = @orgID
				AND i.statusID = @ins_closed
				AND tmp.newNumDaysDelinquent > 0
				AND tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
				AND i.dateDue <= DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());
			END
			

			INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select i.invoiceID, getdate(), @ins_closed, @ins_delinquent, @recordedByMemberID
			from dbo.tr_invoices as i
			inner join #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
			where i.orgID = @orgID
			and i.statusID = @ins_delinquent
			and tmp.newNumDaysDelinquent > 0
			and tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
			and i.dateDue > DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());


			IF @@ROWCOUNT > 0 BEGIN
				UPDATE i
				SET i.statusID = @ins_closed
				FROM dbo.tr_invoices as i
				INNER JOIN #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
				WHERE i.orgID = @orgID
				AND i.statusID = @ins_delinquent
				AND tmp.newNumDaysDelinquent > 0
				AND tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
				AND i.dateDue > DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());
			END
		END
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSolicitationMessages') IS NOT NULL 
		DROP TABLE #tmpSolicitationMessages;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfileUpdates') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfileUpdates;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
