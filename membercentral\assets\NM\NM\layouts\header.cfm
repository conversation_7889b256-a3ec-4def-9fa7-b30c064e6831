<cfoutput>
	<div id="headerContainer">
		<div id="headerContainerInner">
			<div id="topLinks">
				<span id="loginTop" style="float:left; padding:5px 30px 0px 0px;">
					<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<a href="?logout"><img src="/images/logout.png" /></a>
					<cfelse>
						<a href="/?pg=login"><img src="/images/login.png" /></a>
					</cfif>
				</span>
				<span id="calendarTop" style="float:left; padding:5px 30px 0px 0px;"><a href="/?pg=events"><img src="/images/calendar.png" /></a></span>
				<span id="contactUsTop" style="float:left; padding:5px 30px 0px 0px;"><a href="/?pg=contact"><img src="/images/contactUs.png" /></a></span>
			</div>
			<div id="searchArea">
				<form name="searchbox" id="searchbox" action="/?pg=search" method="post">
					<input name="s_a" id="s_a" type="hidden" value="doSearch" /> 
					<input name="s_frm" id="s_frm" type="hidden" value="1" />
					<input class="searchField" type="text" name="s_key_all" id="s_key_all" value="" onFocus="javascript:this.value ='';" /> 
					<button class="searchButton" type="submit" value="Search">Search</button>
				</form>
			</div>
		</div>
		<div id="masthead">
			<a href="/"><img src="/images/mastheadText.png" /></a>
			<div id="mainNav"><cfinclude template="mainNav.cfm"></div>
		</div>
	</div>
</cfoutput>