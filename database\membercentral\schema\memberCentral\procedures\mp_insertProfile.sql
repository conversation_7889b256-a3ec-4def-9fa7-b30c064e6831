ALTER PROCEDURE dbo.mp_insertProfile
@siteID int,
@gatewayID int,
@profileName varchar(100),
@tabTitle varchar(100),
@profileCode varchar(20),
@transLabel varchar(25),
@gatewayUsername varchar(50),
@gatewayPassword varchar(75),
@allowPayments bit,
@allowRefunds bit,
@allowRefundsFromAnyProfile bit,
@GLAccountID int,
@gatewayMerchantId varchar(50),
@allowPayInvoicesOnline bit,
@bankAccountName varchar(200),
@maxFailedAutoAttempts int,
@daysBetweenAutoAttempts int,
@minDaysFailedCleanup int,
@recordedByMemberID int,
@profileID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @sysCreatedContentResourceTypeID int, @PayInstrContentID int, @PayInstrSiteResourceID int, 
		@profileUID uniqueidentifier, @orgID int, @environmentName varchar(12);

	SET @profileID = null;
	SET @profileUID = NEWID();

	SELECT @environmentName = tier 
	FROM dbo.fn_getServerSettings();

	-- enforce unique authorize gateway credentials in prod
	IF @gatewayID = 10 AND @environmentName = 'Production' AND EXISTS (select profileID from dbo.mp_profiles where gatewayID = @gatewayID and [status] <> 'D' AND gatewayUsername + gatewayPassword = @gatewayUsername + @gatewayPassword)
		RAISERROR('Authorize Credentials already in use',16,1);

	BEGIN TRAN;
		INSERT INTO dbo.mp_profiles (siteID, gatewayID, profileName, tabTitle, profileCode, gatewayUsername, 
			gatewayPassword, gatewayMerchantId, allowPayments, allowRefunds, allowRefundsFromAnyProfile, 
			GLAccountID, [status], allowPayInvoicesOnline, bankAccountName, maxFailedAutoAttempts, 
			daysBetweenAutoAttempts, minDaysFailedCleanup, transactionLabel, [uid], frontEndOrderBy)
		VALUES (@siteID, @gatewayID, @profileName, @tabTitle, @profileCode, @gatewayUsername, @gatewayPassword, 
			@gatewayMerchantId, @allowPayments, @allowRefunds, @allowRefundsFromAnyProfile, nullif(@GLAccountID,0), 
			'A', @allowPayInvoicesOnline, @bankAccountName, nullif(@maxFailedAutoAttempts,0), 
			nullif(@daysBetweenAutoAttempts,0), @minDaysFailedCleanup, @transLabel, @profileUID, 999);

		SELECT @profileID = SCOPE_IDENTITY();

		IF @gatewayID = 11 BEGIN
			select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent');

			EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
				@siteResourceStatusID=1, @isHTML=1, @languageID=1, 
				@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=NULL,
				@contentID=@PayInstrContentID OUTPUT, @siteResourceID=@PayInstrSiteResourceID OUTPUT;

			UPDATE dbo.mp_profiles
			SET paymentInstructionsContentID = @PayInstrContentID
			WHERE profileID = @profileID;
		END

		EXEC dbo.mp_reorderProfiles @siteID=@siteID;

		SELECT @orgID = s.orgID
		FROM dbo.sites as s WHERE s.siteID = @siteID;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"PP",
				"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
				"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
				"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
				"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
				"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('' + pp.profileName + 'has been created'),'"','\"') + '" } }'
			FROM dbo.mp_profiles AS pp
			WHERE profileID = @profileID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
