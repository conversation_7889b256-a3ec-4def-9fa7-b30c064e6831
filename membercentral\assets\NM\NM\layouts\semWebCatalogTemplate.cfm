<cfoutput>
	<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
	<!doctype html>
	<html>
		<head>
			<meta charset="utf-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
			<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
			<link rel="stylesheet" href="/css/main.css" />
			#application.objCMS.getBootstrapHeadHTML()#
			#application.objCMS.getResponsiveHeadHTML()#
			<link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
			<link href="/css/responsive.css" rel="stylesheet" type="text/css">
			<link href="/css/font-awesome.min.css" rel="stylesheet" type="text/css">
			<link rel="stylesheet" type="text/css" href="/assets/common/css/mcTabs.css" />
		</head>
	   
		<body>
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
			<!-- wrapper start -->
			<div class="wrapper">
				<!--Header Start-->
				<header id="header" class=" header outer-width">
				  <div class="container">
					<div class="headerTop">
					  <div class="InnerContainer">
						<div class="top-header">

						<div class="top-header-in">
							<div id="topLinks" class="topLinksMenu">
								<span id="loginTop">
									
									<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
										<a href="javascript:void(0);"><img src="/images/loginMenu.png" alt="icon"> <small>Login</small></a>
									<cfelse>
										<a href="/?logout"><img src="/images/loginMenu.png" alt="icon"> <small>Logout</small></a>
									</cfif>
								</span>
								<span id="calendarTop"><a href="javascript:void(0);"><img src="/images/calender.png" alt="icon"><small>calender</small></a></span>
								<span id="contactUsTop"><a href="javascript:void(0);"><img src="/images/contact.png" alt="icon"> <small>Contact Us</small>
									</a></span>
							</div>
							<div id="searchArea">
								<form name="searchbox" id="searchbox" action="/?pg=search" method="post">
									<input name="s_a" id="s_a" type="hidden" value="doSearch" /> 
									<input name="s_frm" id="s_frm" type="hidden" value="1" />
									<input class="searchField" type="text" name="s_key_all" id="s_key_all" value="" onFocus="javascript:this.value ='';" /> 
									<button class="searchButton" type="submit" value="Search">Search</button>
								</form>
							</div>
						</div>
					</div>

					<div class="bottom-header">
						<a href="javascript:void(0);"><i class="fa fa-bars" aria-hidden="true"></i></a>
						<div class="headertext">
							<a href="javascript:void(0);">
								<img src="/images/mastheadText.png" alt="textImg">
							</a>
						</div>
						<div class="menu clearfix">
							<div class="newheader">
								<header>
								  <nav id='cssmenu' class="menuWrap">
									<div id="head-mobile"></div>
									<div class="button"></div>
									<cfif structKeyExists(local.strMenus, "primaryNav")>
										#local.strMenus.primaryNav.menuHTML.rawcontent#
									</cfif>
								  </nav>
								</header>
							</div>
						</div>

					</div>
					  </div>
					</div>
				 </div>
				</header>
				<!--Header End--> 

			  
				<!--Content Start-->
				<div class="InnerContainer">
					<div class="content">
						<div class="container-fluid">
							<div class="row-fluid">
								<div id="links" style="text-align:right;padding-right:25px">
								<b>
								  <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
									 <a href="/?pg=login">Login to Education Portal</a>
									 <cfelse>
									 <a href="/?logout">Logout</a>
								  </cfif>
								</b>
								</div>
								#application.objCMS.renderZone(zone='Main',event=event)#
							</div>
						</div>
					</div>
				</div>
				<!--Content End-->

				<!--Footer Start-->
				<section class="footer">
					<div class="InnerContainer">
						<div class="footer-top">
							<ul>
								<li><a class="mainNav" href="javascript:void(0);">Home</a></li>
								<li><a class="mainNav" href="javascript:void(0);">About</a></li>
								<li><a class="mainNav" href="javascript:void(0);">Find an Attorney</a></li>
								<li><a class="mainNav" href="javascript:void(0);">Membership</a></li>
								<li><a class="mainNav" href="javascript:void(0);">Education</a></li>
								<li><a class="mainNav" href="javascript:void(0);">Publications</a></li>
								<li><a class="mainNav" href="javascript:void(0);">Resources</a></li>
							</ul>
						</div>
						<div class="footer-bottom"><span class="InfoText">NEW MEXICO TRIAL LAWYERS
								ASSOCIATION &amp; FOUNDATION &copy; 2013 <span class="dotSpan">.</span> <a href="javascript:void(0);">Privacy Policy</a></span>
						</div>
					</div>
				</section>
				<!--Footer End--> 
				<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct" and application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
					<div class="noprint">
						<cfoutput>#application.objCMS.renderZone(zone='ToolBar',event=event)#</cfoutput>
					</div>
				</cfif>
			</div>
			<cfelse>
			<div class="content" style="padding:10px;">
				#application.objCMS.renderZone(zone='Main',event=event)#
			</div>
			</cfif>
			<!-- wrapper end --> 
			<!--javascript Files--> 
			<script src="/javascript/custom.js" type="text/javascript"></script>

			<script type="text/javascript">
				(function($) {
					$.fn.menumaker = function(options) {
						var cssmenu = $(this),
							settings = $.extend({
								format: "dropdown",
								sticky: false
							}, options);
						return this.each(function() {
							$(this).find(".button").on('click', function() {
								$(this).toggleClass('menu-opened');
								var mainmenu = $(this).next('ul');
								if (mainmenu.hasClass('open')) {
									mainmenu.slideToggle().removeClass('open');
								} else {
									mainmenu.slideToggle().addClass('open');
									if (settings.format === "dropdown") {
										mainmenu.find('ul').show();
									}
								}
							});
							cssmenu.find('li ul').parent().addClass('has-sub');
							multiTg = function() {
								cssmenu.find(".has-sub").prepend('<span class="submenu-button"></span>');
								cssmenu.find('.submenu-button').on('click', function() {
									$(this).toggleClass('submenu-opened');
									if ($(this).siblings('ul').hasClass('open')) {
										$(this).siblings('ul').removeClass('open').slideToggle();
									} else {
										$(this).siblings('ul').addClass('open').slideToggle();
									}
								});
							};
							if (settings.format === 'multitoggle') multiTg();
							else cssmenu.addClass('dropdown');
							if (settings.sticky === true) cssmenu.css('position', 'fixed');
							resizeFix = function() {
								var mediasize = 767;
								if ($(window).width() > mediasize) {
									cssmenu.find('ul').show();
								}
								if ($(window).width() <= mediasize) {
									cssmenu.find('ul').hide().removeClass('open');
								}
							};
							resizeFix();
							return $(window).on('resize', resizeFix);
						});
					};
				})(jQuery);

				(function($) {
					$(document).ready(function() {
						$("##cssmenu").menumaker({
							format: "multitoggle"
						});
					});
				})(jQuery);

			</script>
		</body>
	</html>
</cfoutput>