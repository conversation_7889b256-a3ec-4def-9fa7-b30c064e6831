ALTER PROC dbo.swtl_getTitlesAndFilesWithinSeminar
@seminarID int,
@catalogOrgCode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	-- Check if this seminar has only one title
	DECLARE @titleCount int;
	SELECT @titleCount = COUNT(DISTINCT t.titleID)
	FROM dbo.tblSeminarsAndTitles AS sat 
	INNER JOIN dbo.tblTitles AS t ON t.titleID = sat.titleID 
	WHERE sat.seminarID = @seminarID
	AND t.isDeleted = 0;
	
	-- dont use titlesInMYCatalog since the title may not be sold separately.
	-- If multiple titles, use original ordering
	IF @titleCount > 1 BEGIN
		SELECT t.titleID, t.titleName, f.fileID, f.fileName, f.fileTitle, ft.filetype, taf.isDownloadable, f.formatsAvailable
		FROM dbo.tblSeminarsAndTitles AS sat
		INNER JOIN dbo.tblTitles AS t ON t.titleID = sat.titleID
		INNER JOIN dbo.tblTitlesAndFiles AS taf ON t.titleID = taf.titleID
		INNER JOIN dbo.tblFiles AS f ON taf.fileID = f.fileID
		INNER JOIN dbo.tblFilesTypes AS ft ON ft.fileTypeID = f.fileTypeID
		WHERE sat.seminarID = @seminarID
		AND t.isDeleted = 0
		AND f.isDeleted = 0
		ORDER BY sat.titleOrder, t.titleName, taf.fileOrder;
	END
	ELSE BEGIN
		-- For single title, group by file type: media files first, downloadable files second, PVR images third
		SELECT t.titleID, t.titleName, f.fileID, f.fileName, f.fileTitle, ft.filetype, taf.isDownloadable, f.formatsAvailable
		FROM dbo.tblSeminarsAndTitles AS sat
		INNER JOIN dbo.tblTitles AS t ON t.titleID = sat.titleID
		INNER JOIN dbo.tblTitlesAndFiles AS taf ON t.titleID = taf.titleID
		INNER JOIN dbo.tblFiles AS f ON taf.fileID = f.fileID
		INNER JOIN dbo.tblFilesTypes AS ft ON ft.fileTypeID = f.fileTypeID
		WHERE sat.seminarID = @seminarID
		AND t.isDeleted = 0
		AND f.isDeleted = 0
		ORDER BY
			-- Group by file type: 1=media, 2=downloadable, 3=PVR
			CASE
				WHEN ft.fileType IN ('video', 'audio') THEN 1  -- Media files first
				WHEN ft.fileType = 'paper' AND f.fileName = 'Slides' THEN 3  -- PVR images third
				ELSE 2  -- Other downloadable files second
			END,
			taf.fileOrder;  -- Within each group, maintain original order
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
